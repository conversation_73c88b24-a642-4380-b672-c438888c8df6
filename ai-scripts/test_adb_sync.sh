#!/bin/bash
# test_adb_sync.sh - 測試 ADB 同步腳本的修復

# 色彩定義
BLUE='\033[0;34m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🧪 ADB 同步腳本測試工具${NC}"
echo -e "${BLUE}================================${NC}"

# 檢查腳本是否存在
SCRIPT_PATH="shell/adb_rsync_like_push.sh"
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}❌ 找不到腳本檔案：${NC}$SCRIPT_PATH"
    exit 1
fi

echo -e "${GREEN}✓ 找到腳本檔案${NC}"

# 檢查腳本語法
echo -e "${BLUE}🔍 檢查腳本語法...${NC}"
if bash -n "$SCRIPT_PATH"; then
    echo -e "${GREEN}✓ 腳本語法正確${NC}"
else
    echo -e "${RED}❌ 腳本語法錯誤${NC}"
    exit 1
fi

# 檢查必要的命令
echo -e "${BLUE}🔍 檢查必要命令...${NC}"
REQUIRED_COMMANDS=("adb" "bc" "numfmt" "ping")
MISSING_COMMANDS=()

for cmd in "${REQUIRED_COMMANDS[@]}"; do
    if command -v "$cmd" > /dev/null; then
        echo -e "${GREEN}✓ $cmd${NC}"
    else
        echo -e "${RED}❌ $cmd${NC}"
        MISSING_COMMANDS+=("$cmd")
    fi
done

# 檢查可選命令
echo -e "${BLUE}🔍 檢查可選命令...${NC}"
OPTIONAL_COMMANDS=("pv" "gnuplot" "osascript")

for cmd in "${OPTIONAL_COMMANDS[@]}"; do
    if command -v "$cmd" > /dev/null; then
        echo -e "${GREEN}✓ $cmd (可選)${NC}"
    else
        echo -e "${YELLOW}⚠️ $cmd (可選，未安裝)${NC}"
    fi
done

if [ ${#MISSING_COMMANDS[@]} -gt 0 ]; then
    echo -e "${RED}❌ 缺少必要命令：${MISSING_COMMANDS[*]}${NC}"
    echo -e "${BLUE}💡 安裝建議：${NC}"
    for cmd in "${MISSING_COMMANDS[@]}"; do
        case "$cmd" in
            "adb")
                echo -e "  brew install android-platform-tools"
                ;;
            "bc")
                echo -e "  brew install bc"
                ;;
            "numfmt")
                echo -e "  brew install coreutils"
                ;;
            "ping")
                echo -e "  ping 應該是系統內建命令"
                ;;
        esac
    done
    exit 1
fi

# 測試腳本參數解析
echo -e "${BLUE}🧪 測試參數解析...${NC}"

# 測試 1: 無參數
echo -e "${BLUE}測試 1: 無參數${NC}"
if bash "$SCRIPT_PATH" 2>&1 | grep -q "用法:"; then
    echo -e "${GREEN}✓ 無參數時正確顯示用法${NC}"
else
    echo -e "${RED}❌ 無參數時未正確顯示用法${NC}"
fi

# 測試 2: 只有一個參數
echo -e "${BLUE}測試 2: 只有一個參數${NC}"
if bash "$SCRIPT_PATH" "all" 2>&1 | grep -q "用法:"; then
    echo -e "${GREEN}✓ 單一參數時正確顯示用法${NC}"
else
    echo -e "${RED}❌ 單一參數時未正確顯示用法${NC}"
fi

# 測試 3: 檢查 all 參數轉換（乾跑模式）
echo -e "${BLUE}測試 3: 檢查 all 參數轉換${NC}"
# 創建一個測試目錄
TEST_DIR="/tmp/adb_test_$(date +%s)"
mkdir -p "$TEST_DIR"
echo "test" > "$TEST_DIR/test.mp4"

# 修改腳本進行乾跑測試（只檢查參數解析，不實際連接裝置）
if timeout 10s bash "$SCRIPT_PATH" "all" "$TEST_DIR" 2>&1 | grep -q "all.*已轉換為.*kt,fj"; then
    echo -e "${GREEN}✓ 'all' 參數正確轉換為 'kt,fj'${NC}"
else
    echo -e "${YELLOW}⚠️ 無法驗證 'all' 參數轉換（可能因為裝置連線問題）${NC}"
fi

# 清理測試目錄
rm -rf "$TEST_DIR"

echo -e "${BLUE}🔍 檢查腳本關鍵功能...${NC}"

# 檢查函式定義
FUNCTIONS=("check_device_connection" "format_speed" "format_time" "sync_to_device" "resolve_device_ip" "optimize_device")
for func in "${FUNCTIONS[@]}"; do
    if grep -q "^$func()" "$SCRIPT_PATH"; then
        echo -e "${GREEN}✓ 函式 $func 已定義${NC}"
    else
        echo -e "${RED}❌ 函式 $func 未找到${NC}"
    fi
done

# 檢查修復的關鍵部分
echo -e "${BLUE}🔍 檢查修復內容...${NC}"

# 檢查 gnuplot 修復
if grep -q "檢查是否可以產生傳輸速度圖表" "$SCRIPT_PATH"; then
    echo -e "${GREEN}✓ gnuplot 圖表生成已修復${NC}"
else
    echo -e "${RED}❌ gnuplot 圖表生成修復未找到${NC}"
fi

# 檢查 all 參數調試
if grep -q "原始目標參數" "$SCRIPT_PATH"; then
    echo -e "${GREEN}✓ 'all' 參數調試資訊已加入${NC}"
else
    echo -e "${RED}❌ 'all' 參數調試資訊未找到${NC}"
fi

# 檢查裝置連線增強
if grep -q "網路連通性" "$SCRIPT_PATH"; then
    echo -e "${GREEN}✓ 裝置連線檢查已增強${NC}"
else
    echo -e "${RED}❌ 裝置連線檢查增強未找到${NC}"
fi

# 檢查 macOS 通知
if grep -q "osascript" "$SCRIPT_PATH"; then
    echo -e "${GREEN}✓ macOS 通知功能已加入${NC}"
else
    echo -e "${RED}❌ macOS 通知功能未找到${NC}"
fi

echo -e "${BLUE}================================${NC}"
echo -e "${GREEN}🎉 測試完成！${NC}"
echo -e "${BLUE}💡 建議：${NC}"
echo -e "  1. 確保目標裝置已開啟 ADB 除錯模式"
echo -e "  2. 確保裝置與電腦在同一網路"
echo -e "  3. 檢查腳本中的 IP 位址設定是否正確"
echo -e "  4. 如需圖表功能，請安裝：brew install gnuplot"
echo -e "  5. 如需進度顯示，請安裝：brew install pv"
