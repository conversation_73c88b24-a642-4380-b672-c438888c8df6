#!/bin/bash
# adb_rsync_like_push.sh
# 新用法：
# ./adb_rsync_like_push.sh all "/Users/<USER>/Downloads/test3/11_機制的上半場_24 /Users/<USER>/Downloads/test3/12_閃耀的她_32 /Users/<USER>/Downloads/test3/14_你是我的永恆星辰_22 /Users/<USER>/Downloads/test3/15_我，喜歡你_24 /Users/<USER>/Downloads/test3/16_陪你逐風飛翔_35 /Users/<USER>/Downloads/test3/17_城中之城_40 /Users/<USER>/Downloads/test3/18_玫瑰之戰_40 /Users/<USER>/Downloads/test3/19_我的前半生_42"
# ./adb_rsync_like_push.sh all "/Users/<USER>/Downloads/test3/19_我的前半生_42"
# 所有目的地將自動設置為 /sdcard/Download/ 下的相同名稱資料夾

# ./adb_rsync_like_push.sh all "/Users/<USER>/Downloads/test3/37_愛在星空下_46"
# 客廳
# ./adb_rsync_like_push.sh kt  "/Users/<USER>/Downloads/test3/37_愛在星空下_46"
# 媽媽房間
# ./adb_rsync_like_push.sh fj  "/Users/<USER>/Downloads/test3/37_愛在星空下_46"

# 在腳本開頭添加
trap cleanup INT TERM EXIT

cleanup() {
    echo "正在清理並終止所有 ADB 程序..."
    killall -9 adb 2>/dev/null
    exit 1
}

# ✅ 裝置 IP 對照表

# 家裡
# DEVICE_IP_KT="*************"   # 家裡-客廳
# DEVICE_IP_FJ="*************"   # 家裡-媽媽房間

# 店裡
DEVICE_IP_KT="*************"     # 店裡-客廳
DEVICE_IP_FJ="*************"     # 店裡-媽媽房間


# ✅ 全域設定
MAX_PARALLEL=2       # 最大並行處理數
RETRY_COUNT=3        # 失敗重試次數
LOG_DIR="$HOME/Downloads/adb_sync_logs"  # 日誌存放目錄
ADB_BUFFER_SIZE=4M   # ADB 傳輸緩衝區大小，可提高到 4M, 8M 或 16M
SHOW_PROGRESS=true   # 是否顯示進度條

# ✅ 色彩定義
BLUE='\033[0;34m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'  # No Color

# 確保日誌目錄存在
mkdir -p "$LOG_DIR"

# ✅ 讀取參數：目標裝置代號（可多個，以逗號分隔）和來源資料夾（可多個，以空格分隔）
targets="$1"
LOCAL_DIRS="$2"

# 檢查參數
if [ -z "$targets" ] || [ -z "$LOCAL_DIRS" ]; then
    echo -e "${RED}❌ 用法:${NC} $0 <客廳|房間|kt|fj|all|kt,fj> \"<本地資料夾1> <本地資料夾2> ...\""
    echo -e "\n${BLUE}📘 範例：${NC}"
    echo "  $0 客廳 \"~/影片/4月/1.你比星光美麗 ~/影片/4月/2.星空下的約定\""
    echo "  $0 kt,fj \"~/影片/4月/1.你比星光美麗 ~/影片/4月/2.星空下的約定\""
    echo "  $0 all \"~/影片/4月/1.你比星光美麗 ~/影片/4月/2.星空下的約定\""
    exit 1
fi

# ✅ 健康檢查函式
check_device_connection() {
    local DEVICE_IP=$1
    local DEVICE="$DEVICE_IP:5555"

    echo -e "${BLUE}🔍 檢查裝置連線：${NC}$DEVICE_IP"

    # 先檢查網路連通性
    if ! ping -c 1 -W 3000 "$DEVICE_IP" > /dev/null 2>&1; then
        echo -e "${RED}❌ 網路無法連通到裝置：${NC}$DEVICE_IP"
        return 1
    fi
    echo -e "${GREEN}✓ 網路連通正常${NC}"

    # 檢查裝置是否可連接
    echo -e "${BLUE}🔗 嘗試連接 ADB...${NC}"
    local connect_output=$(adb connect "$DEVICE_IP:5555" 2>&1)
    echo -e "${BLUE}ADB 連接回應：${NC}$connect_output"

    if [[ "$connect_output" == *"connected"* ]] || [[ "$connect_output" == *"already connected"* ]]; then
        echo -e "${GREEN}✓ ADB 連接成功${NC}"
    else
        echo -e "${RED}❌ ADB 連接失敗：${NC}$connect_output"
        return 1
    fi

    # 檢查裝置是否在線
    echo -e "${BLUE}🧪 測試裝置回應...${NC}"
    if ! adb -s "$DEVICE" shell echo "Connection test" > /dev/null 2>&1; then
        echo -e "${RED}❌ 裝置離線或無回應：${NC}$DEVICE_IP"

        # 嘗試重新連接
        echo -e "${YELLOW}🔄 嘗試重新連接...${NC}"
        adb disconnect "$DEVICE_IP:5555" > /dev/null 2>&1
        sleep 2
        if adb connect "$DEVICE_IP:5555" > /dev/null 2>&1; then
            if adb -s "$DEVICE" shell echo "Connection test" > /dev/null 2>&1; then
                echo -e "${GREEN}✓ 重新連接成功${NC}"
                return 0
            fi
        fi
        return 1
    fi

    echo -e "${GREEN}✅ 裝置連線檢查通過${NC}"
    return 0
}

# ✅ 顯示速度單位
format_speed() {
    local bytes=$1
    local seconds=$2
    
    if [ "$seconds" -eq 0 ]; then
        echo "計算中..."
        return
    fi
    
    local speed=$(echo "scale=2; $bytes / $seconds" | bc)
    
    if [ $(echo "$speed >= 1048576" | bc) -eq 1 ]; then
        echo "$(echo "scale=2; $speed / 1048576" | bc) MB/s"
    elif [ $(echo "$speed >= 1024" | bc) -eq 1 ]; then
        echo "$(echo "scale=2; $speed / 1024" | bc) KB/s"
    else
        echo "$speed B/s"
    fi
}

# ✅ 格式化時間
format_time() {
    local seconds=$1
    
    if [ "$seconds" -lt 60 ]; then
        echo "${seconds}秒"
    elif [ "$seconds" -lt 3600 ]; then
        local mins=$(($seconds / 60))
        local secs=$(($seconds % 60))
        echo "${mins}分${secs}秒"
    else
        local hrs=$(($seconds / 3600))
        local mins=$((($seconds % 3600) / 60))
        local secs=$(($seconds % 60))
        echo "${hrs}時${mins}分${secs}秒"
    fi
}

# ✅ 顯示進度條函式
show_progress_bar() {
    local percent=$1
    local width=50
    local filled=$((width * percent / 100))
    local empty=$((width - filled))
    
    printf "["
    printf "%${filled}s" | tr ' ' '#'
    printf "%${empty}s" | tr ' ' ' '
    printf "] %3d%%\r" $percent
}

# ✅ 裝置列表函式
sync_to_device() {
    local DEVICE_IP=$1
    local LOCAL_DIR=$2
    
    # 自動取得資料夾名稱作為目標資料夾名稱
    local FOLDER_NAME=$(basename "$LOCAL_DIR")
    local REMOTE_DIR="/sdcard/Download/$FOLDER_NAME"
    
    local DEVICE="$DEVICE_IP:5555"
    local TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    local LOG_FILE="$LOG_DIR/adb_sync_${DEVICE_IP}_${FOLDER_NAME}_${TIMESTAMP}.log"
    local FAILED_FILE="$LOG_DIR/adb_sync_${DEVICE_IP}_${FOLDER_NAME}_${TIMESTAMP}_failed.txt"
    local STATS_FILE="$LOG_DIR/adb_sync_${DEVICE_IP}_${FOLDER_NAME}_${TIMESTAMP}_stats.csv"
    
    # 建立統計檔案標頭
    echo "檔案名稱,檔案大小(bytes),傳輸時間(秒),傳輸速度(bytes/s),開始時間,結束時間" > "$STATS_FILE"
    
    # 檢查連線
    if ! check_device_connection "$DEVICE_IP"; then
        echo -e "${RED}❌ 裝置連接失敗，跳過同步：${NC}$DEVICE_IP" | tee -a "$LOG_FILE"
        return 1
    fi
    
    # 優化 ADB 設定
    adb -s "$DEVICE" shell "setprop sys.usb.config rndis,mtp,adb" >> "$LOG_FILE" 2>&1
    
    echo -e "${YELLOW}🕒 $(date '+%F %T') 傳送開始${NC}" | tee -a "$LOG_FILE"
    echo "來源: $LOCAL_DIR" | tee -a "$LOG_FILE"
    echo "目標: $DEVICE:$REMOTE_DIR" | tee -a "$LOG_FILE"
    echo -e "${BLUE}📂 本地資料夾：${NC}$LOCAL_DIR"
    echo -e "${BLUE}📡 傳送到裝置 [$DEVICE_IP]：${NC}$REMOTE_DIR"
    
    # ✅ 顯示遠端剩餘空間（開始前）
    echo -e "${YELLOW}💽 查詢 [$DEVICE_IP] 遠端剩餘空間（傳輸前）...${NC}" | tee -a "$LOG_FILE"
    
    # 檢查遠端資料夾是否存在，不存在則創建
    if ! adb -s "$DEVICE" shell "[ -d \"$REMOTE_DIR\" ]" > /dev/null 2>&1; then
        echo -e "${YELLOW}📁 遠端目錄不存在，正在創建...${NC}" | tee -a "$LOG_FILE"
        adb -s "$DEVICE" shell "mkdir -p \"$REMOTE_DIR\"" >> "$LOG_FILE" 2>&1
    fi
    
    adb -s "$DEVICE" shell "df -h /sdcard" | tee -a "$LOG_FILE"
    
    # 檢查裝置連線類型
    local CONNECTION_TYPE=$(adb -s "$DEVICE" shell "getprop sys.usb.state" | tr -d '\r\n')
    local WIFI_SPEED=$(adb -s "$DEVICE" shell "dumpsys wifi | grep 'Link speed'" | grep -o '[0-9]\+ Mbps' | head -1)
    
    echo -e "${BLUE}🔌 連線類型：${NC}$CONNECTION_TYPE" | tee -a "$LOG_FILE"
    if [ -n "$WIFI_SPEED" ]; then
        echo -e "${BLUE}📶 WiFi 速度：${NC}$WIFI_SPEED" | tee -a "$LOG_FILE"
    fi
    
    # 蒐集檔案清單
    echo -e "${BLUE}🔍 掃描本地檔案...${NC}" | tee -a "$LOG_FILE"
    FILE_LIST=()
    TOTAL_BYTES=0
    
    while IFS= read -r -d '' file; do
        if [[ "$file" == *.mp4 || "$file" == */folder.jpg || "$file" == */cover.jpg ]]; then
            FILE_LIST+=("$file")
            # 計算總檔案大小
            size=$(stat -f %z "$file")
            TOTAL_BYTES=$((TOTAL_BYTES + size))
        fi
    done < <(find "$LOCAL_DIR" -type f -print0)
    
    LOCAL_COUNT=${#FILE_LIST[@]}
    echo -e "${BLUE}📄 本地傳輸檔案數量（.mp4 + 封面圖）：${NC}$LOCAL_COUNT" | tee -a "$LOG_FILE"
    echo -e "${BLUE}💾 預計傳輸總量：${NC}$(numfmt --to=iec-i --suffix=B $TOTAL_BYTES)" | tee -a "$LOG_FILE"
    
    if [ "$LOCAL_COUNT" -eq 0 ]; then
        echo -e "${YELLOW}⚠️ 沒有找到符合條件的檔案${NC}" | tee -a "$LOG_FILE"
        return 0
    fi
    
    # 檢查遠端空間是否足夠
    local DEVICE_FREE=$(adb -s "$DEVICE" shell "df /sdcard | tail -1" | awk '{print $4}')

    # 轉換為位元組單位（如果返回的是 KB 單位）
    if [[ "$DEVICE_FREE" =~ ^[0-9]+$ ]]; then
        # 假設 df 返回的是 KB 單位
        DEVICE_FREE=$((DEVICE_FREE * 1024))
    else
        echo -e "${YELLOW}⚠️ 無法獲取遠端空間資訊，跳過空間檢查${NC}" | tee -a "$LOG_FILE"
        DEVICE_FREE=$((TOTAL_BYTES * 2))  # 假設有足夠空間
    fi

    # 顯示實際獲取的空間信息（以 GB 為單位）
    local DEVICE_FREE_GB=$(echo "scale=2; $DEVICE_FREE / 1073741824" | bc)
    echo -e "${BLUE}💾 遠端可用空間：${NC}${DEVICE_FREE_GB}GB (${DEVICE_FREE} bytes)" | tee -a "$LOG_FILE"

    if [ "$TOTAL_BYTES" -gt "$DEVICE_FREE" ]; then
        echo -e "${RED}❌ 遠端空間不足！需要：$(numfmt --to=iec-i --suffix=B $TOTAL_BYTES)，可用：$(numfmt --to=iec-i --suffix=B $DEVICE_FREE)${NC}" | tee -a "$LOG_FILE"
        return 1
    fi
    
    REMOTE_COUNT=0
    SKIPPED_COUNT=0
    FAILED_COUNT=0
    TOTAL_TRANSFERRED=0
    START_TIME=$(date +%s)
    
    # 建立暫存檔案，儲存傳輸速度資訊
    SPEED_FILE=$(mktemp)
    echo "0" > "$SPEED_FILE"  # 初始化速度為 0
    
    for i in "${!FILE_LIST[@]}"; do
        file="${FILE_LIST[$i]}"
        index=$((i + 1))
        REL_PATH="${file#$LOCAL_DIR/}"
        REMOTE_PATH="$REMOTE_DIR/$REL_PATH"
        REMOTE_DIR_PATH=$(dirname "$REMOTE_PATH")
        FILE_SIZE=$(stat -f %z "$file")
        FILE_SIZE_FORMATTED=$(numfmt --to=iec-i --suffix=B $FILE_SIZE)
        
        # 計算和顯示進度
        PERCENT=$((100 * index / LOCAL_COUNT))
        ELAPSED=$(($(date +%s) - START_TIME))
        
        if [ "$ELAPSED" -gt 0 ] && [ "$TOTAL_TRANSFERRED" -gt 0 ]; then
            AVG_SPEED=$(echo "scale=2; $TOTAL_TRANSFERRED / $ELAPSED" | bc)
            CURRENT_SPEED=$(cat "$SPEED_FILE")
            SPEED_FORMATTED=$(format_speed $CURRENT_SPEED 1)
            AVG_SPEED_FORMATTED=$(format_speed $TOTAL_TRANSFERRED $ELAPSED)
            
            REMAINING_BYTES=$((TOTAL_BYTES - TOTAL_TRANSFERRED))
            if [ "$AVG_SPEED" != "0" ] && [ "$AVG_SPEED" != "" ]; then
                ETA=$(echo "scale=0; $REMAINING_BYTES / $AVG_SPEED" | bc)
                ETA_FORMATTED=$(format_time $ETA)
            else
                ETA_FORMATTED="計算中..."
            fi
        else
            SPEED_FORMATTED="計算中..."
            AVG_SPEED_FORMATTED="計算中..."
            ETA_FORMATTED="計算中..."
        fi
        
        echo -e "\n${YELLOW}📌 檔案 ${index}/${LOCAL_COUNT} (${PERCENT}%)${NC}" | tee -a "$LOG_FILE"
        echo -e "${BLUE}⏱️ 已耗時：${NC}$(format_time $ELAPSED) ${BLUE}| 預計剩餘：${NC}$ETA_FORMATTED" | tee -a "$LOG_FILE"
        echo -e "${BLUE}🚀 當前速度：${NC}$SPEED_FORMATTED ${BLUE}| 平均速度：${NC}$AVG_SPEED_FORMATTED" | tee -a "$LOG_FILE"
        echo -e "${BLUE}📦 檔案：${NC}$REL_PATH (${FILE_SIZE_FORMATTED})" | tee -a "$LOG_FILE"
        
        if $SHOW_PROGRESS; then
            show_progress_bar $PERCENT
        fi
        
        # 檢查檔案是否需要傳輸
        local NEED_TRANSFER=false
        local FILE_EXISTS=$(adb -s "$DEVICE" shell "[ -f \"$REMOTE_PATH\" ] && echo exists" | tr -d '\r')
        
        if [ "$FILE_EXISTS" = "exists" ]; then
            local REMOTE_SIZE=$(adb -s "$DEVICE" shell "stat -c %s \"$REMOTE_PATH\" 2>/dev/null" | tr -d '\r')
            
            if [ "$FILE_SIZE" -eq "$REMOTE_SIZE" ]; then
                # 只有在檔案大小相同時才考慮略過
                # 對於大檔案，可以選擇只比較大小而不比較 MD5，提高效率
                if [ "$FILE_SIZE" -gt 104857600 ]; then  # 大於 100MB 的檔案
                    NEED_TRANSFER=false
                    echo -e "${YELLOW}💾 大型檔案大小相同，略過 MD5 檢查${NC}" | tee -a "$LOG_FILE"
                else
                    LOCAL_MD5=$(md5 -q "$file")
                    REMOTE_MD5=$(adb -s "$DEVICE" shell "md5sum \"$REMOTE_PATH\" 2>/dev/null | awk '{print \$1}'" | tr -d '\r')
                    
                    if [ "$LOCAL_MD5" != "$REMOTE_MD5" ]; then
                        NEED_TRANSFER=true
                        echo -e "${YELLOW}🔄 MD5 不符，需要更新${NC}" | tee -a "$LOG_FILE"
                    fi
                fi
            else
                NEED_TRANSFER=true
                echo -e "${YELLOW}🔄 檔案大小不符，需要更新 (本地: ${FILE_SIZE}, 遠端: ${REMOTE_SIZE})${NC}" | tee -a "$LOG_FILE"
            fi
        else
            NEED_TRANSFER=true
            echo -e "${YELLOW}🆕 遠端檔案不存在${NC}" | tee -a "$LOG_FILE"
        fi
        
        if $NEED_TRANSFER; then
            echo -e "${GREEN}🚀 [$DEVICE_IP] 傳送：$REL_PATH${NC}" | tee -a "$LOG_FILE"
            
            # 確保目標目錄存在
            adb -s "$DEVICE" shell "mkdir -p \"$REMOTE_DIR_PATH\"" >> "$LOG_FILE" 2>&1
            
            # 如果遠端檔案存在，則先刪除
            if [ "$FILE_EXISTS" = "exists" ]; then
                echo -e "${YELLOW}🗑️ 刪除舊檔案：$REMOTE_PATH${NC}" | tee -a "$LOG_FILE"
                adb -s "$DEVICE" shell "rm -f \"$REMOTE_PATH\"" >> "$LOG_FILE" 2>&1
            fi
            
            # 記錄傳輸開始時間
            local TRANSFER_START=$(date +%s)
            local TRANSFER_START_FORMATTED=$(date '+%F %T')
            
            # 設定 ADB 緩衝區大小並啟用壓縮
            export ADB_TRACE=all  # 追蹤 ADB 行為以診斷問題
            
            # 重試機制
            local success=false
            for ((retry=1; retry<=RETRY_COUNT; retry++)); do
                if [ "$retry" -gt 1 ]; then
                    echo -e "${YELLOW}🔄 重試 $retry/$RETRY_COUNT...${NC}" | tee -a "$LOG_FILE"
                fi
                
                # 使用管道和 pv 顯示進度
                if command -v pv > /dev/null; then
                    # 使用 pv 命令顯示傳輸進度
                    pv -pterb "$file" | adb -s "$DEVICE" push - "$REMOTE_PATH" >> "$LOG_FILE" 2>&1 && success=true && break
                else
                    # 使用增強的 adb push 參數 (緩衝區大小)
                    TMPFILE=$(mktemp)
                    (adb -s "$DEVICE" push --sync "$file" "$REMOTE_PATH" 2>&1 | tee "$TMPFILE" >> "$LOG_FILE") && success=true && break
                    # 嘗試從輸出提取傳輸速度
                    SPEED=$(grep -o '[0-9.]\+ MB/s' "$TMPFILE" | tail -1 | grep -o '[0-9.]\+')
                    if [ -n "$SPEED" ]; then
                        SPEED_BPS=$(echo "$SPEED * 1048576" | bc)
                        echo "$SPEED_BPS" > "$SPEED_FILE"
                    fi
                    rm "$TMPFILE"
                fi
                
                sleep 1
            done
            
            # 記錄傳輸結束時間和計算傳輸速度
            local TRANSFER_END=$(date +%s)
            local TRANSFER_END_FORMATTED=$(date '+%F %T')
            local TRANSFER_DURATION=$((TRANSFER_END - TRANSFER_START))
            
            # 避免除以零
            if [ "$TRANSFER_DURATION" -eq 0 ]; then
                TRANSFER_DURATION=1
            fi
            
            local TRANSFER_SPEED=$(echo "scale=2; $FILE_SIZE / $TRANSFER_DURATION" | bc)
            echo "$TRANSFER_SPEED" > "$SPEED_FILE"  # 更新最新的傳輸速度
            
            # 記錄到統計檔案
            echo "\"$REL_PATH\",$FILE_SIZE,$TRANSFER_DURATION,$TRANSFER_SPEED,\"$TRANSFER_START_FORMATTED\",\"$TRANSFER_END_FORMATTED\"" >> "$STATS_FILE"
            
            if $success; then
                REMOTE_COUNT=$((REMOTE_COUNT + 1))
                TOTAL_TRANSFERRED=$((TOTAL_TRANSFERRED + FILE_SIZE))
                local SPEED_FORMATTED=$(format_speed $TRANSFER_SPEED 1)
                echo -e "${GREEN}✅ 傳送成功 - 耗時：$(format_time $TRANSFER_DURATION) - 速度：$SPEED_FORMATTED${NC}" | tee -a "$LOG_FILE"
            else
                FAILED_COUNT=$((FAILED_COUNT + 1))
                echo -e "${RED}❌ 傳送失敗${NC}" | tee -a "$LOG_FILE"
                echo "$REL_PATH" >> "$FAILED_FILE"
            fi
        else
            SKIPPED_COUNT=$((SKIPPED_COUNT + 1))
            echo -e "${GREEN}✅ 無變化：$REL_PATH${NC}" | tee -a "$LOG_FILE"
        fi
    done
    
    # 刪除暫存檔案
    rm -f "$SPEED_FILE"
    
    # 統計和報告
    TOTAL_TIME=$(($(date +%s) - START_TIME))
    TOTAL_TIME_FORMATTED=$(format_time $TOTAL_TIME)
    
    # 計算平均傳輸速度
    if [ "$TOTAL_TIME" -gt 0 ] && [ "$TOTAL_TRANSFERRED" -gt 0 ]; then
        AVG_SPEED=$(echo "scale=2; $TOTAL_TRANSFERRED / $TOTAL_TIME" | bc)
        AVG_SPEED_FORMATTED=$(format_speed $TOTAL_TRANSFERRED $TOTAL_TIME)
    else
        AVG_SPEED_FORMATTED="N/A"
    fi
    
    echo -e "\n${BLUE}📊 同步統計 [$DEVICE_IP] - ${FOLDER_NAME}:${NC}" | tee -a "$LOG_FILE"
    echo -e "${BLUE}📦 預期傳輸檔案數：${NC}$LOCAL_COUNT" | tee -a "$LOG_FILE"
    echo -e "${GREEN}📥 實際傳送檔案數：${NC}$REMOTE_COUNT" | tee -a "$LOG_FILE"
    echo -e "${YELLOW}⏭️ 略過不變檔案數：${NC}$SKIPPED_COUNT" | tee -a "$LOG_FILE"
    echo -e "${RED}❌ 傳送失敗檔案數：${NC}$FAILED_COUNT" | tee -a "$LOG_FILE"
    echo -e "${BLUE}💾 傳輸總量：${NC}$(numfmt --to=iec-i --suffix=B $TOTAL_TRANSFERRED)" | tee -a "$LOG_FILE"
    echo -e "${BLUE}⏱️ 總耗時：${NC}$TOTAL_TIME_FORMATTED" | tee -a "$LOG_FILE"
    echo -e "${BLUE}🚀 平均傳輸速度：${NC}$AVG_SPEED_FORMATTED" | tee -a "$LOG_FILE"
    
    # ✅ 顯示遠端剩餘空間（結束後）
    echo -e "${YELLOW}💽 查詢 [$DEVICE_IP] 遠端剩餘空間（傳輸後）...${NC}" | tee -a "$LOG_FILE"
    adb -s "$DEVICE" shell "df -h /sdcard" | tee -a "$LOG_FILE"
    
    if [ "$FAILED_COUNT" -gt 0 ]; then
        echo -e "${RED}⚠️ 有 $FAILED_COUNT 個檔案傳送失敗，查看失敗清單：${NC}$FAILED_FILE" | tee -a "$LOG_FILE"
    else
        echo -e "${GREEN}✅ 傳輸全部完成${NC}" | tee -a "$LOG_FILE"
    fi
    
    echo -e "${BLUE}📝 紀錄已儲存於：${NC}$LOG_FILE"
    echo -e "${BLUE}📊 傳輸統計已儲存於：${NC}$STATS_FILE"
    
    # 產生效能報告 - 修復 Rex 停住的問題
    if [ "$REMOTE_COUNT" -gt 0 ]; then
        echo -e "${BLUE}📈 檢查是否可以產生傳輸速度圖表...${NC}"

        # 檢查 gnuplot 是否可用
        if command -v gnuplot > /dev/null 2>&1; then
            echo -e "${BLUE}✓ 找到 gnuplot，開始產生圖表...${NC}"

            local PLOT_FILE="$LOG_DIR/adb_sync_${DEVICE_IP}_${FOLDER_NAME}_${TIMESTAMP}_speed.png"

            # 檢查統計檔案是否存在且有內容
            if [ -f "$STATS_FILE" ] && [ -s "$STATS_FILE" ]; then
                local LINE_COUNT=$(wc -l < "$STATS_FILE")
                echo -e "${BLUE}📊 統計檔案有 $LINE_COUNT 行資料${NC}"

                if [ "$LINE_COUNT" -gt 1 ]; then  # 至少要有標頭 + 1 行資料
                    # 創建 gnuplot 腳本
                    local GNUPLOT_SCRIPT=$(mktemp)
                    cat > "$GNUPLOT_SCRIPT" << 'EOL'
set terminal png size 800,400
set output "PLOT_FILE_PLACEHOLDER"
set title "ADB 傳輸速度統計 - DEVICE_IP_PLACEHOLDER - FOLDER_NAME_PLACEHOLDER"
set xlabel "檔案編號"
set ylabel "傳輸速度 (MB/s)"
set grid
set style data linespoints
set key outside
set datafile separator ","
plot "STATS_FILE_PLACEHOLDER" using 0:(column(4)/1048576) skip 1 title "檔案傳輸速度" with linespoints lc rgb "blue"
EOL

                    # 替換佔位符
                    sed -i '' "s|PLOT_FILE_PLACEHOLDER|$PLOT_FILE|g" "$GNUPLOT_SCRIPT"
                    sed -i '' "s|DEVICE_IP_PLACEHOLDER|$DEVICE_IP|g" "$GNUPLOT_SCRIPT"
                    sed -i '' "s|FOLDER_NAME_PLACEHOLDER|$FOLDER_NAME|g" "$GNUPLOT_SCRIPT"
                    sed -i '' "s|STATS_FILE_PLACEHOLDER|$STATS_FILE|g" "$GNUPLOT_SCRIPT"

                    echo -e "${BLUE}🔧 執行 gnuplot...${NC}"
                    if gnuplot "$GNUPLOT_SCRIPT" 2>/dev/null; then
                        if [ -f "$PLOT_FILE" ]; then
                            echo -e "${GREEN}✅ 傳輸速度圖表已生成：${NC}$PLOT_FILE"
                        else
                            echo -e "${YELLOW}⚠️ gnuplot 執行完成但未找到輸出檔案${NC}"
                        fi
                    else
                        echo -e "${YELLOW}⚠️ gnuplot 執行失敗，跳過圖表生成${NC}"
                    fi

                    rm -f "$GNUPLOT_SCRIPT"
                else
                    echo -e "${YELLOW}⚠️ 統計檔案資料不足，跳過圖表生成${NC}"
                fi
            else
                echo -e "${YELLOW}⚠️ 統計檔案不存在或為空，跳過圖表生成${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️ 未安裝 gnuplot，跳過圖表生成${NC}"
            echo -e "${BLUE}💡 提示：可使用 'brew install gnuplot' 安裝${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 沒有傳輸檔案，跳過圖表生成${NC}"
    fi
}

# ✅ 指定裝置代號對應
resolve_device_ip() {
    case "$1" in
        客廳|kt) echo "$DEVICE_IP_KT" ;;
        房間|fj) echo "$DEVICE_IP_FJ" ;;
        *) echo -e "${RED}❌ 無效裝置代號：${NC}$1" >&2 ; return 1 ;;
    esac
}

# ✅ 優化裝置傳輸設定
optimize_device() {
    local DEVICE_IP=$1
    local DEVICE="$DEVICE_IP:5555"

    echo -e "${BLUE}🔧 優化裝置 [$DEVICE_IP] 的 ADB 傳輸設定...${NC}"

    # 先檢查裝置連線
    if ! check_device_connection "$DEVICE_IP"; then
        echo -e "${RED}❌ 裝置連線失敗，跳過優化：${NC}$DEVICE_IP"
        return 1
    fi

    # 設定 USB 配置為高效模式
    echo -e "${BLUE}  ⚙️ 設定 USB 配置...${NC}"
    if adb -s "$DEVICE" shell "setprop sys.usb.config rndis,mtp,adb" > /dev/null 2>&1; then
        echo -e "${GREEN}  ✓ USB 配置設定完成${NC}"
    else
        echo -e "${YELLOW}  ⚠️ USB 配置設定失敗（可能無權限）${NC}"
    fi

    # 提高 ADB 優先級（可能需要 root 權限）
    echo -e "${BLUE}  ⚙️ 嘗試提高 ADB 優先級...${NC}"
    if adb -s "$DEVICE" shell "echo -17 > /proc/self/oom_adj" > /dev/null 2>&1; then
        echo -e "${GREEN}  ✓ ADB 優先級設定完成${NC}"
    else
        echo -e "${YELLOW}  ⚠️ ADB 優先級設定失敗（需要 root 權限）${NC}"
    fi

    # 提高 CPU 和 IO 優先級（可能需要 root 權限）
    echo -e "${BLUE}  ⚙️ 嘗試提高 CPU/IO 優先級...${NC}"
    if adb -s "$DEVICE" shell "renice -n -10 \$(pgrep adbd)" > /dev/null 2>&1; then
        echo -e "${GREEN}  ✓ CPU/IO 優先級設定完成${NC}"
    else
        echo -e "${YELLOW}  ⚠️ CPU/IO 優先級設定失敗（需要 root 權限）${NC}"
    fi

    echo -e "${GREEN}✅ 裝置優化完成${NC}"
    return 0
}

# ✅ 若為 all 則轉成清單
echo -e "${BLUE}🔍 原始目標參數：${NC}$targets"
if [ "$targets" = "all" ]; then
    targets="kt,fj"
    echo -e "${BLUE}✓ 'all' 已轉換為：${NC}$targets"
fi

# ✅ 拆解多裝置逗號清單
IFS=',' read -ra DEVICE_KEYS <<< "$targets"
echo -e "${BLUE}🔄 開始同步到 ${#DEVICE_KEYS[@]} 個裝置...${NC}"

# 顯示所有要處理的裝置
echo -e "${BLUE}📱 裝置清單：${NC}"
for i in "${!DEVICE_KEYS[@]}"; do
    key="${DEVICE_KEYS[$i]}"
    DEVICE_IP=$(resolve_device_ip "$key")
    if [ -n "$DEVICE_IP" ]; then
        echo -e "  $((i+1)). $key -> $DEVICE_IP"
    else
        echo -e "  $((i+1)). $key -> ${RED}❌ 無效裝置代號${NC}"
    fi
done

# 先優化所有設備的傳輸設定
echo -e "${BLUE}🔧 開始優化裝置設定...${NC}"
for key in "${DEVICE_KEYS[@]}"; do
    DEVICE_IP=$(resolve_device_ip "$key")
    if [ -n "$DEVICE_IP" ]; then
        echo -e "${BLUE}🔧 正在優化裝置：${NC}$key ($DEVICE_IP)"
        optimize_device "$DEVICE_IP"
    else
        echo -e "${RED}❌ 跳過無效裝置：${NC}$key"
    fi
done

# 拆解多個來源資料夾
IFS=' ' read -ra LOCAL_DIR_ARRAY <<< "$LOCAL_DIRS"
echo -e "${BLUE}📂 共有 ${#LOCAL_DIR_ARRAY[@]} 個來源資料夾要同步${NC}"

# 建立一個二維陣列，存放所有裝置與資料夾的組合
echo -e "${BLUE}🔗 建立裝置與資料夾配對...${NC}"
DEVICE_DIR_PAIRS=()
VALID_DEVICE_COUNT=0

for key in "${DEVICE_KEYS[@]}"; do
    DEVICE_IP=$(resolve_device_ip "$key")
    if [ -n "$DEVICE_IP" ]; then
        VALID_DEVICE_COUNT=$((VALID_DEVICE_COUNT + 1))
        echo -e "${BLUE}📱 處理裝置：${NC}$key ($DEVICE_IP)"

        for dir in "${LOCAL_DIR_ARRAY[@]}"; do
            # 檢查本地目錄存在
            if [ ! -d "$dir" ]; then
                echo -e "${RED}❌ 本地資料夾不存在：${NC}$dir"
                continue
            fi

            echo -e "${BLUE}  📂 加入配對：${NC}$DEVICE_IP -> $dir"
            DEVICE_DIR_PAIRS+=("$DEVICE_IP:$dir")
        done
    else
        echo -e "${RED}❌ 無效裝置代號，跳過：${NC}$key"
    fi
done

echo -e "${BLUE}📊 配對統計：${NC}"
echo -e "  有效裝置數：$VALID_DEVICE_COUNT"
echo -e "  總配對數：${#DEVICE_DIR_PAIRS[@]}"

if [ ${#DEVICE_DIR_PAIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ 沒有有效的裝置與資料夾配對，程式結束${NC}"
    exit 1
fi

# 創建一個 FIFO 用於控制並行數量
FIFO=$(mktemp -u)
mkfifo "$FIFO"
exec 3<>"$FIFO"
rm "$FIFO"

# 初始化並行控制槽
for ((i=1; i<=MAX_PARALLEL; i++)); do
    echo >&3
done

# 啟動同步程序
echo -e "${BLUE}🚀 開始並行同步處理...${NC}"
SYNC_SUCCESS_COUNT=0
SYNC_FAIL_COUNT=0

for i in "${!DEVICE_DIR_PAIRS[@]}"; do
    pair="${DEVICE_DIR_PAIRS[$i]}"

    # 等待一個可用槽
    read -u 3

    # 拆分裝置 IP 和資料夾路徑
    IFS=':' read -r DEVICE_IP LOCAL_DIR <<< "$pair"

    {
        echo -e "\n${BLUE}🚀 [$((i+1))/${#DEVICE_DIR_PAIRS[@]}] 開始處理裝置：${NC}$DEVICE_IP"
        echo -e "${BLUE}📂 處理資料夾：${NC}$LOCAL_DIR"

        # 記錄開始時間
        TASK_START=$(date +%s)
        TASK_START_FORMATTED=$(date '+%F %T')

        if sync_to_device "$DEVICE_IP" "$LOCAL_DIR"; then
            TASK_END=$(date +%s)
            TASK_DURATION=$((TASK_END - TASK_START))
            echo -e "${GREEN}✅ [$((i+1))/${#DEVICE_DIR_PAIRS[@]}] 同步完成：${NC}$DEVICE_IP -> $(basename "$LOCAL_DIR") (耗時: $(format_time $TASK_DURATION))"
            SYNC_SUCCESS_COUNT=$((SYNC_SUCCESS_COUNT + 1))
        else
            TASK_END=$(date +%s)
            TASK_DURATION=$((TASK_END - TASK_START))
            echo -e "${RED}❌ [$((i+1))/${#DEVICE_DIR_PAIRS[@]}] 同步失敗：${NC}$DEVICE_IP -> $(basename "$LOCAL_DIR") (耗時: $(format_time $TASK_DURATION))"
            SYNC_FAIL_COUNT=$((SYNC_FAIL_COUNT + 1))
        fi

        # 釋放一個槽位
        echo >&3
    } &
done

# 等待所有背景工作完成
echo -e "${BLUE}⏳ 等待所有同步工作完成...${NC}"
wait

# 關閉文件描述符
exec 3>&-

# 最終統計
echo -e "\n${BLUE}📊 最終同步統計：${NC}"
echo -e "${GREEN}✅ 成功：${NC}$SYNC_SUCCESS_COUNT 個任務"
echo -e "${RED}❌ 失敗：${NC}$SYNC_FAIL_COUNT 個任務"
echo -e "${BLUE}📱 總計：${NC}$((SYNC_SUCCESS_COUNT + SYNC_FAIL_COUNT)) 個任務"

if [ "$SYNC_FAIL_COUNT" -eq 0 ]; then
    echo -e "${GREEN}🎉 所有同步工作完成！${NC}"

    # 發送 macOS 通知
    if command -v osascript > /dev/null; then
        osascript -e "display notification \"所有 $((SYNC_SUCCESS_COUNT + SYNC_FAIL_COUNT)) 個同步任務已完成\" with title \"ADB 同步完成\" sound name \"Glass\""
    fi
else
    echo -e "${YELLOW}⚠️ 部分同步工作失敗，請檢查日誌檔案${NC}"

    # 發送 macOS 通知
    if command -v osascript > /dev/null; then
        osascript -e "display notification \"$SYNC_SUCCESS_COUNT 個成功，$SYNC_FAIL_COUNT 個失敗\" with title \"ADB 同步完成（有錯誤）\" sound name \"Basso\""
    fi
fi